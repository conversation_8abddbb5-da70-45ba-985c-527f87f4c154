import express from 'express';
import { stripeService } from '../services/stripeService';
import { authenticateToken } from '../middleware/auth';
import { supabase } from '../services/supabaseService';

const router = express.Router();

// Create or get Stripe customer
router.post('/customer', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { email, name } = req.body;

    if (!userId || !email) {
      return res.status(400).json({ error: 'User ID and email are required' });
    }

    const customerId = await stripeService.createOrGetCustomer(userId, email, name);
    
    res.json({ customerId });
  } catch (error) {
    console.error('Error creating/getting customer:', error);
    res.status(500).json({ error: 'Failed to create or get customer' });
  }
});

// Create subscription
router.post('/subscription', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { priceId, email, name } = req.body;

    if (!userId || !priceId || !email) {
      return res.status(400).json({ error: 'User ID, price ID, and email are required' });
    }

    const customerId = await stripeService.createOrGetCustomer(userId, email, name);
    const subscription = await stripeService.createSubscription(customerId, priceId);
    
    res.json({ subscription });
  } catch (error) {
    console.error('Error creating subscription:', error);
    res.status(500).json({ error: 'Failed to create subscription' });
  }
});

// Get user subscriptions
router.get('/subscriptions', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.json({ subscriptions: [] });
    }

    const subscriptions = await stripeService.getCustomerSubscriptions(profile.stripe_customer_id);
    
    res.json({ subscriptions });
  } catch (error) {
    console.error('Error getting subscriptions:', error);
    res.status(500).json({ error: 'Failed to get subscriptions' });
  }
});

// Cancel subscription
router.post('/subscription/:id/cancel', authenticateToken, async (req, res) => {
  try {
    const { id: subscriptionId } = req.params;
    const userId = req.user?.id;

    if (!userId || !subscriptionId) {
      return res.status(400).json({ error: 'User ID and subscription ID are required' });
    }

    // Verify subscription belongs to user
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    const subscriptions = await stripeService.getCustomerSubscriptions(profile.stripe_customer_id);
    const subscription = subscriptions.find(sub => sub.id === subscriptionId);

    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }

    const canceledSubscription = await stripeService.cancelSubscription(subscriptionId);
    
    res.json({ subscription: canceledSubscription });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    res.status(500).json({ error: 'Failed to cancel subscription' });
  }
});

// Create payment intent for credit purchase
router.post('/credit-purchase', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { amount, credits, email, name } = req.body;

    if (!userId || !amount || !credits || !email) {
      return res.status(400).json({ error: 'User ID, amount, credits, and email are required' });
    }

    const customerId = await stripeService.createOrGetCustomer(userId, email, name);
    const paymentIntent = await stripeService.createCreditPurchaseIntent(customerId, amount, credits);
    
    res.json({ 
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id 
    });
  } catch (error) {
    console.error('Error creating credit purchase:', error);
    res.status(500).json({ error: 'Failed to create credit purchase' });
  }
});

// Get customer invoices
router.get('/invoices', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.json({ invoices: [] });
    }

    const invoices = await stripeService.getCustomerInvoices(profile.stripe_customer_id, limit);
    
    res.json({ invoices });
  } catch (error) {
    console.error('Error getting invoices:', error);
    res.status(500).json({ error: 'Failed to get invoices' });
  }
});

// Get customer payment methods
router.get('/payment-methods', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user's Stripe customer ID
    const { data: profile } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', userId)
      .single();

    if (!profile?.stripe_customer_id) {
      return res.json({ paymentMethods: [] });
    }

    const paymentMethods = await stripeService.getCustomerPaymentMethods(profile.stripe_customer_id);
    
    res.json({ paymentMethods });
  } catch (error) {
    console.error('Error getting payment methods:', error);
    res.status(500).json({ error: 'Failed to get payment methods' });
  }
});

// Get Stripe prices
router.get('/prices', async (req, res) => {
  try {
    const prices = await stripeService.getPrices();
    
    res.json({ prices });
  } catch (error) {
    console.error('Error getting prices:', error);
    res.status(500).json({ error: 'Failed to get prices' });
  }
});

// Webhook endpoint for Stripe events
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    
    if (!signature) {
      return res.status(400).json({ error: 'Missing stripe-signature header' });
    }

    await stripeService.handleWebhook(req.body, signature);
    
    res.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(400).json({ error: 'Webhook signature verification failed' });
  }
});

export default router;
