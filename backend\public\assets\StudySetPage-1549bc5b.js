import{r as g,j as e,c as fe,B as S,a as ee,b as W,H as be,d as ve,e as je,u as we}from"./index-204383b4.js";import{u as ae}from"./studyStore-1a8f6dc7.js";import{u as Ne}from"./documentStore-9e7af976.js";import{u as ne}from"./useUserSettings-dae1d1cc.js";const ie=({selectedDocuments:r,onSelectionChange:h,maxSelection:i=5})=>{const{documents:x,fetchDocuments:o,isLoading:u}=Ne(),[n,C]=g.useState("");g.useEffect(()=>{x.length===0&&o()},[x.length,o]);const v=x.filter(c=>c.is_processed&&c.filename.toLowerCase().includes(n.toLowerCase())),A=c=>{r.includes(c)?h(r.filter(b=>b!==c)):r.length<i&&h([...r,c])},F=()=>x.filter(c=>r.includes(c.id));return u?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):x.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Search documents...",value:n,onChange:c=>C(c.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),r.length>0&&e.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[e.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",r.length," of ",i," documents:"]}),e.jsx("div",{className:"space-y-1",children:F().map(c=>e.jsxs("div",{className:"text-sm text-gray-300 flex items-center justify-between",children:[e.jsx("span",{className:"truncate",children:c.filename}),e.jsx("button",{onClick:()=>A(c.id),className:"text-red-400 hover:text-red-300 ml-2",children:"✕"})]},c.id))})]}),e.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:v.map(c=>{const w=r.includes(c.id),b=!w&&r.length<i;return e.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${w?"bg-primary-500/20 border-primary-500":b?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>b||w?A(c.id):null,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg",children:c.file_type==="pdf"?"📄":c.file_type==="docx"?"📝":c.file_type==="txt"?"📃":"📊"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-white font-medium truncate",children:c.filename}),e.jsxs("p",{className:"text-sm text-gray-400",children:[c.file_type.toUpperCase()," • ",Math.round(c.file_size/1024)," KB"]})]})]})}),e.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${w?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:w&&e.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},c.id)})}),v.length===0&&n&&e.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})};var d;(function(r){r.EASY="easy",r.MEDIUM="medium",r.HARD="hard",r.COLLEGE="college",r.GRADUATE="graduate",r.PHD="phd"})(d||(d={}));var I;(function(r){r.SHORT="short",r.MEDIUM="medium",r.LONG="long"})(I||(I={}));const X=r=>({[d.EASY]:"Easy",[d.MEDIUM]:"Medium",[d.HARD]:"Hard",[d.COLLEGE]:"College",[d.GRADUATE]:"Graduate",[d.PHD]:"PhD"})[r],le=r=>({[d.EASY]:1,[d.MEDIUM]:3,[d.HARD]:4,[d.COLLEGE]:5,[d.GRADUATE]:6,[d.PHD]:7})[r],oe=r=>{switch(r){case 1:return d.EASY;case 2:return d.EASY;case 3:return d.MEDIUM;case 4:return d.HARD;case 5:return d.COLLEGE;case 6:return d.GRADUATE;case 7:return d.PHD;default:return d.MEDIUM}},ke=[{value:d.EASY,label:"Easy",description:"Basic facts and definitions"},{value:d.MEDIUM,label:"Medium",description:"Moderate understanding required"},{value:d.HARD,label:"Hard",description:"Deep analysis and critical thinking"},{value:d.COLLEGE,label:"College",description:"Undergraduate level complexity"},{value:d.GRADUATE,label:"Graduate",description:"Advanced graduate study"},{value:d.PHD,label:"PhD",description:"Research-level expertise"}],_e=r=>{switch(r){case d.EASY:return"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50";case d.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case d.HARD:return"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50";case d.COLLEGE:return"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50";case d.GRADUATE:return"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50";case d.PHD:return"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},Se=r=>{switch(r){case d.EASY:return"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20";case d.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case d.HARD:return"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20";case d.COLLEGE:return"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20";case d.GRADUATE:return"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20";case d.PHD:return"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},Ce=({value:r,onChange:h,className:i="",disabled:x=!1,label:o="Difficulty Level"})=>e.jsxs("div",{className:`space-y-3 ${i}`,children:[e.jsx("label",{className:"block text-sm font-medium text-text-primary",children:o}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:ke.map(u=>{const n=r===u.value,C=n?Se(u.value):_e(u.value);return e.jsxs("button",{type:"button",onClick:()=>!x&&h(u.value),disabled:x,className:`
                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${C}
                ${x?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${n?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:u.description,"aria-pressed":n,children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold",children:u.label}),e.jsx("div",{className:`text-xs mt-1 ${n?"text-white/90":"text-text-secondary"}`,children:u.description})]}),n&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},u.value)})}),e.jsx("p",{className:"text-xs text-text-muted",children:"Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated."})]}),Ee=[{value:I.SHORT,label:"Short",description:"Concise answers (1-2 sentences)",icon:"📝"},{value:I.MEDIUM,label:"Medium",description:"Balanced detail (2-3 sentences)",icon:"📄"},{value:I.LONG,label:"Long",description:"Comprehensive answers (3-5 sentences)",icon:"📋"}],Ae=r=>{switch(r){case I.SHORT:return"bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50";case I.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case I.LONG:return"bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},Fe=r=>{switch(r){case I.SHORT:return"bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20";case I.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case I.LONG:return"bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},De=({value:r,onChange:h,className:i="",disabled:x=!1,label:o="Content Length"})=>e.jsxs("div",{className:`space-y-3 ${i}`,children:[e.jsx("label",{className:"block text-sm font-medium text-text-primary",children:o}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:Ee.map(u=>{const n=r===u.value,C=n?Fe(u.value):Ae(u.value);return e.jsxs("button",{type:"button",onClick:()=>!x&&h(u.value),disabled:x,className:`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${C}
                ${x?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${n?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:u.description,"aria-pressed":n,children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("div",{className:"text-2xl",children:u.icon}),e.jsx("div",{className:"font-semibold",children:u.label}),e.jsx("div",{className:`text-xs ${n?"text-white/90":"text-text-secondary"}`,children:u.description})]}),n&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},u.value)})}),e.jsx("p",{className:"text-xs text-text-muted",children:"Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations."})]}),$e=fe(r=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async h=>{r({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");r({generationProgress:"Generating flashcards with AI..."});const x=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(h)});if(!x.ok){const u=await x.json();throw new Error(u.error||"Generation failed")}const o=await x.json();if(o.success)return r({lastGenerated:{studySet:o.data.studySet,content:o.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""}),{studySet:o.data.studySet,flashcards:o.data.flashcards,creditsRemaining:o.data.creditsRemaining};throw new Error(o.error)}catch(i){throw r({isGenerating:!1,generationProgress:""}),i}},generateQuiz:async h=>{r({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");r({generationProgress:"Generating quiz questions with AI..."});const x=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(h)});if(!x.ok){const u=await x.json();throw new Error(u.error||"Generation failed")}const o=await x.json();if(o.success)return r({lastGenerated:{studySet:o.data.studySet,content:o.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""}),{studySet:o.data.studySet,questions:o.data.questions,creditsRemaining:o.data.creditsRemaining};throw new Error(o.error)}catch(i){throw r({isGenerating:!1,generationProgress:""}),i}},generateMoreFlashcards:async h=>{r({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");r({generationProgress:"Generating additional flashcards with AI..."});const x=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(h)});if(!x.ok){const u=await x.json();throw new Error(u.error||"Generation failed")}const o=await x.json();if(o.success)return r({isGenerating:!1,generationProgress:""}),{flashcards:o.data.flashcards,creditsRemaining:o.data.creditsRemaining};throw new Error(o.error)}catch(i){throw r({isGenerating:!1,generationProgress:""}),i}},clearLastGenerated:()=>{r({lastGenerated:null})}})),qe=({selectedCount:r,totalCount:h,onDeleteSelected:i,onClearSelection:x,isLoading:o=!1,className:u=""})=>r===0?null:e.jsx("div",{className:`bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ${u}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:[r," of ",h," flashcard",r!==1?"s":""," selected"]}),e.jsx("button",{onClick:x,className:"text-gray-400 hover:text-white text-sm underline",disabled:o,children:"Clear selection"})]}),e.jsx("div",{className:"flex items-center space-x-3",children:e.jsx(S,{onClick:i,variant:"danger",size:"sm",isLoading:o,disabled:o,className:"px-4 py-2",children:"Delete Selected"})})]})}),Te=({studySetId:r,flashcards:h,onFlashcardAdded:i,onFlashcardUpdated:x,onFlashcardDeleted:o,onFlashcardsGenerated:u})=>{const{alert:n,confirm:C}=ee(),{user:v}=W(),{generateMoreFlashcards:A}=$e(),{settings:F,updateSettings:c}=ne(),[w,b]=g.useState(!1),[O,L]=g.useState([]),[q,D]=g.useState(10),[M,N]=g.useState(""),[T,p]=g.useState(d.MEDIUM),[E,U]=g.useState(I.MEDIUM),[R,m]=g.useState(!1),[k,H]=g.useState(!1),[G,B]=g.useState({front:"",back:"",difficulty_level:3}),[V,J]=g.useState(null),[$,P]=g.useState({front:"",back:"",difficulty_level:3}),[j,Q]=g.useState([]),[Y,t]=g.useState(!1),[s,l]=g.useState(!1),[f,K]=g.useState(!1),Z=(a,y)=>{y?Q(_=>[..._,a]):(Q(_=>_.filter(z=>z!==a)),t(!1))},ce=a=>{t(a),Q(a?h.map(y=>y.id):[])},te=()=>{Q([]),t(!1)},de=async()=>{if(j.length!==0){if(!(F!=null&&F.skip_delete_confirmations)){let a=!1;if(!await C({title:"Delete Flashcards",message:`Are you sure you want to delete ${j.length} flashcard${j.length!==1?"s":""}?`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:_=>{a=_}}))return;if(a)try{await c({skip_delete_confirmations:!0})}catch(_){console.error("Failed to update user settings:",_)}}await ue()}},ue=async()=>{l(!0);try{const a=await fetch("/api/flashcards/bulk-delete",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`,"Content-Type":"application/json"},body:JSON.stringify({flashcardIds:j})});if(!a.ok){const z=await a.json();throw new Error(z.error||"Failed to delete flashcards")}const y=await a.json(),{deletedCount:_}=y.data;j.forEach(z=>o(z)),te(),await n({title:"Success",message:`${_} flashcard${_!==1?"s":""} deleted successfully!`,variant:"success"})}catch(a){await n({title:"Error",message:a.message||"Failed to delete flashcards",variant:"error"})}finally{l(!1)}},me=async()=>{if(!G.front.trim()||!G.back.trim()){await n({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const a=await fetch(`/api/flashcards/study-set/${r}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:G.front.trim(),back:G.back.trim(),difficulty_level:G.difficulty_level,is_ai_generated:!1})});if(!a.ok)throw new Error("Failed to create flashcard");const y=await a.json();i(y.data),B({front:"",back:"",difficulty_level:3}),H(!1),await n({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(a){await n({title:"Error",message:a.message||"Failed to add flashcard",variant:"error"})}},he=a=>{J(a),P({front:a.front,back:a.back,difficulty_level:typeof a.difficulty_level=="string"?le(a.difficulty_level):a.difficulty_level||3})},xe=async()=>{if(V){if(!$.front.trim()||!$.back.trim()){await n({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const a=await fetch(`/api/flashcards/${V.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:$.front.trim(),back:$.back.trim(),difficulty_level:$.difficulty_level})});if(!a.ok)throw new Error("Failed to update flashcard");const y=await a.json();x(y.data),J(null),P({front:"",back:"",difficulty_level:3}),await n({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(a){await n({title:"Error",message:a.message||"Failed to update flashcard",variant:"error"})}}},ge=()=>{J(null),P({front:"",back:"",difficulty_level:3})},pe=async a=>{if(F!=null&&F.skip_delete_confirmations){await re(a);return}let y=!1;if(await C({title:"Delete Flashcard",message:`Are you sure you want to delete this flashcard?

Front: ${a.front.substring(0,50)}${a.front.length>50?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:z=>{y=z}})){if(y)try{await c({skip_delete_confirmations:!0})}catch(z){console.error("Failed to update user settings:",z)}await re(a)}},re=async a=>{try{if(!(await fetch(`/api/flashcards/${a.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete flashcard");o(a.id),await n({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(y){await n({title:"Error",message:y.message||"Failed to delete flashcard",variant:"error"})}},se=()=>q*2,ye=async()=>{if(O.length===0){await n({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});return}const a=se();if(v&&v.credits_remaining<a){await n({title:"Insufficient Credits",message:`You need ${a} credits to generate ${q} flashcards, but you only have ${v.credits_remaining} credits remaining.`,variant:"error"});return}if(await C({title:"Generate Flashcards",message:`Generate ${q} flashcards from ${O.length} document(s)?

This will cost ${a} credits.`,confirmText:"Generate",cancelText:"Cancel"})){m(!0);try{const _=await A({studySetId:r,documentIds:O,count:q,customPrompt:M.trim()||void 0,difficultyLevel:T,contentLength:E});u(_.flashcards),v&&W.getState().updateUser({credits_remaining:_.creditsRemaining}),await n({title:"Success",message:`Generated ${_.flashcards.length} flashcards successfully!`,variant:"success"}),L([]),N(""),b(!1)}catch(_){await n({title:"Generation Error",message:_.message||"Failed to generate flashcards",variant:"error"})}finally{m(!1)}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(S,{onClick:()=>H(!k),variant:"secondary",size:"sm",children:"➕ Add Flashcard"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"AI Mode"}),e.jsx("button",{onClick:()=>b(!w),className:`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${w?"bg-primary-500":"bg-gray-600"}
              `,children:e.jsx("span",{className:`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${w?"translate-x-6":"translate-x-1"}
                `})})]})]})]}),k&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:G.front,onChange:a=>B(y=>({...y,front:a.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:G.back,onChange:a=>B(y=>({...y,back:a.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:G.difficulty_level,onChange:a=>B(y=>({...y,difficulty_level:parseInt(a.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:me,variant:"primary",children:"Add Flashcard"}),e.jsx(S,{onClick:()=>H(!1),variant:"secondary",children:"Cancel"})]})]})]}),V&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:$.front,onChange:a=>P(y=>({...y,front:a.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:$.back,onChange:a=>P(y=>({...y,back:a.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:$.difficulty_level,onChange:a=>P(y=>({...y,difficulty_level:parseInt(a.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:xe,variant:"primary",children:"Save Changes"}),e.jsx(S,{onClick:ge,variant:"secondary",children:"Cancel"})]})]})]}),w&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(ie,{selectedDocuments:O,onSelectionChange:L,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Flashcards"}),e.jsx("input",{type:"number",min:"1",max:"50",value:q,onChange:a=>D(parseInt(a.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[se()," credits"]})]})]}),e.jsx(Ce,{value:T,onChange:p}),e.jsx(De,{value:E,onChange:U}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:M,onChange:a=>N(a.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),e.jsx(S,{onClick:ye,disabled:O.length===0||R,className:"w-full",variant:"primary",children:R?"Generating...":`Generate ${q} Flashcards`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",h.length,")"]}),h.length>0&&e.jsx("div",{className:"flex items-center space-x-3",children:e.jsxs("label",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[e.jsx("input",{type:"checkbox",checked:Y,onChange:a=>ce(a.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"}),e.jsx("span",{children:"Select All"})]})})]}),e.jsx(qe,{selectedCount:j.length,totalCount:h.length,onDeleteSelected:de,onClearSelection:te,isLoading:s}),h.length>0&&e.jsx("div",{className:"flex justify-end mb-4",children:e.jsx("button",{onClick:()=>K(!f),className:"flex items-center space-x-2 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-primary-500 transition-colors",children:f?e.jsxs(e.Fragment,{children:[e.jsx(be,{className:"w-4 h-4"}),e.jsx("span",{children:"Hide Back Content"})]}):e.jsxs(e.Fragment,{children:[e.jsx(ve,{className:"w-4 h-4"}),e.jsx("span",{children:"Show Back Content"})]})})}),h.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:h.map(a=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 pt-1",children:e.jsx("input",{type:"checkbox",checked:j.includes(a.id),onChange:y=>Z(a.id,y.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),e.jsx("p",{className:"text-white font-medium",children:a.front})]}),f&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),e.jsx("p",{className:"text-gray-300",children:a.back})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[a.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),a.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",typeof a.difficulty_level=="string"?X(a.difficulty_level):X(oe(a.difficulty_level))]}),e.jsxs("span",{children:["Reviewed: ",a.times_reviewed||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>he(a),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"✏️"}),e.jsx("button",{onClick:()=>pe(a),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"🗑️"})]})]})})]})},a.id))})]})]})},Ge=({studySetId:r,questions:h,onQuestionAdded:i,onQuestionUpdated:x,onQuestionDeleted:o,onQuestionsGenerated:u})=>{const{alert:n,confirm:C}=ee(),{user:v}=W(),[A,F]=g.useState(!1),[c,w]=g.useState([]),[b,O]=g.useState(10),[L,q]=g.useState(""),[D,M]=g.useState(!1),[N,T]=g.useState(!1),[p,E]=g.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[U,R]=g.useState(null),[m,k]=g.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),H=async()=>{if(!p.question_text.trim()){await n({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(p.correct_answers.length===0){await n({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((p.question_type==="multiple_choice"||p.question_type==="select_all")&&p.options.filter(s=>s.trim().length>0).length<2){await n({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/study-set/${r}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:p.question_text.trim(),question_type:p.question_type,options:p.question_type==="multiple_choice"||p.question_type==="select_all"?p.options.filter(l=>l.trim().length>0):null,correct_answers:p.correct_answers,explanation:p.explanation.trim()||null,difficulty_level:p.difficulty_level})});if(!t.ok)throw new Error("Failed to create question");const s=await t.json();i(s.data),E({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),T(!1),await n({title:"Success",message:"Question added successfully!",variant:"success"})}catch(t){await n({title:"Error",message:t.message||"Failed to add question",variant:"error"})}},G=async t=>{if(await C({title:"Delete Question",message:`Are you sure you want to delete this question?

${t.question_text.substring(0,100)}${t.question_text.length>100?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/quiz-questions/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete question");o(t.id),await n({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(l){await n({title:"Error",message:l.message||"Failed to delete question",variant:"error"})}},B=t=>{R(t),k({question_text:t.question_text,question_type:t.question_type,options:t.options||["","","",""],correct_answers:t.correct_answers,explanation:t.explanation||"",difficulty_level:typeof t.difficulty_level=="string"?le(t.difficulty_level):t.difficulty_level||3})},V=async()=>{if(U){if(!m.question_text.trim()){await n({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(m.correct_answers.length===0){await n({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((m.question_type==="multiple_choice"||m.question_type==="select_all")&&m.options.filter(s=>s.trim().length>0).length<2){await n({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/${U.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:m.question_text.trim(),question_type:m.question_type,options:m.question_type==="multiple_choice"||m.question_type==="select_all"?m.options.filter(l=>l.trim().length>0):null,correct_answers:m.correct_answers,explanation:m.explanation.trim()||null,difficulty_level:m.difficulty_level})});if(!t.ok)throw new Error("Failed to update question");const s=await t.json();x(s.data),R(null),await n({title:"Success",message:"Question updated successfully!",variant:"success"})}catch(t){await n({title:"Error",message:t.message||"Failed to update question",variant:"error"})}}},J=()=>{R(null),k({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3})},$=()=>b*3,P=async()=>{if(c.length===0){await n({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});return}const t=$();if(v&&v.credits_remaining<t){await n({title:"Insufficient Credits",message:`You need ${t} credits to generate ${b} questions, but you only have ${v.credits_remaining} credits remaining.`,variant:"warning"});return}if(await C({title:"Generate Questions",message:`Generate ${b} questions for ${t} credits?`,confirmText:"Generate",cancelText:"Cancel"})){M(!0);try{const l=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({studySetId:r,documentIds:c,count:b,customPrompt:L.trim()||void 0})});if(!l.ok)throw new Error("Failed to generate questions");const f=await l.json();u(f.data.questions),v&&W.getState().updateUser({credits_remaining:f.data.creditsRemaining}),await n({title:"Success",message:`Generated ${f.data.questions.length} questions successfully!`,variant:"success"}),w([]),q(""),F(!1)}catch(l){await n({title:"Error",message:l.message||"Failed to generate questions",variant:"error"})}finally{M(!1)}}},j=t=>{E(s=>({...s,question_type:t,options:t==="multiple_choice"||t==="select_all"?["","","",""]:[],correct_answers:[]}))},Q=(t,s)=>{E(l=>({...l,options:l.options.map((f,K)=>K===t?s:f)}))},Y=t=>{E(s=>{const l=s.correct_answers.includes(t);return s.question_type==="multiple_choice"?{...s,correct_answers:l?[]:[t]}:{...s,correct_answers:l?s.correct_answers.filter(f=>f!==t):[...s.correct_answers,t]}})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(S,{onClick:()=>T(!N),variant:"secondary",size:"sm",children:N?"Cancel":"Add Question"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"AI Mode"}),e.jsx("button",{onClick:()=>F(!A),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${A?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${A?"translate-x-6":"translate-x-1"}`})})]})]})]}),N&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:p.question_text,onChange:t=>E(s=>({...s,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:p.question_type,onChange:t=>j(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(p.question_type==="multiple_choice"||p.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:p.options.map((t,s)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>Y(t),className:`flex-shrink-0 w-5 h-5 border-2 ${p.question_type==="multiple_choice"?"rounded-full":"rounded"} ${p.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:p.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:l=>Q(s,l.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${s+1}`})]},s))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:p.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),p.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>E(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${p.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>E(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${p.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),p.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:p.correct_answers.join(`
`),onChange:t=>E(s=>({...s,correct_answers:t.target.value.split(`
`).filter(l=>l.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:p.explanation,onChange:t=>E(s=>({...s,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:p.difficulty_level,onChange:t=>E(s=>({...s,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:H,variant:"primary",children:"Add Question"}),e.jsx(S,{onClick:()=>T(!1),variant:"secondary",children:"Cancel"})]})]})]}),U&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:m.question_text,onChange:t=>k(s=>({...s,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:m.question_type,onChange:t=>k(s=>({...s,question_type:t.target.value,options:t.target.value==="multiple_choice"||t.target.value==="select_all"?["","","",""]:[],correct_answers:[]})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(m.question_type==="multiple_choice"||m.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:m.options.map((t,s)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>{const l=m.correct_answers.includes(t);m.question_type==="multiple_choice"?k(f=>({...f,correct_answers:l?[]:[t]})):k(f=>({...f,correct_answers:l?f.correct_answers.filter(K=>K!==t):[...f.correct_answers,t]}))},className:`flex-shrink-0 w-5 h-5 border-2 ${m.question_type==="multiple_choice"?"rounded-full":"rounded"} ${m.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:m.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:l=>k(f=>({...f,options:f.options.map((K,Z)=>Z===s?l.target.value:K)})),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${s+1}`})]},s))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:m.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),m.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>k(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${m.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>k(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${m.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),m.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:m.correct_answers.join(`
`),onChange:t=>k(s=>({...s,correct_answers:t.target.value.split(`
`).filter(l=>l.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:m.explanation,onChange:t=>k(s=>({...s,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:m.difficulty_level,onChange:t=>k(s=>({...s,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:V,variant:"primary",children:"Save Changes"}),e.jsx(S,{onClick:J,variant:"secondary",children:"Cancel"})]})]})]}),A&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(ie,{selectedDocuments:c,onSelectionChange:w,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Questions"}),e.jsx("input",{type:"number",min:"1",max:"50",value:b,onChange:t=>O(parseInt(t.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[$()," credits"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:L,onChange:t=>q(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),e.jsx(S,{onClick:P,disabled:c.length===0||D,className:"w-full",variant:"primary",children:D?"Generating...":`Generate ${b} Questions`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Questions (",h.length,")"]}),h.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:h.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),e.jsx("p",{className:"text-white font-medium",children:t.question_text})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),e.jsx("p",{className:"text-gray-300 capitalize",children:t.question_type.replace("_"," ")})]}),t.options&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),e.jsx("ul",{className:"text-gray-300 text-sm",children:t.options.map((s,l)=>e.jsxs("li",{className:`${t.correct_answers.includes(s)?"text-green-400 font-medium":""}`,children:[l+1,". ",s]},`${t.id}-option-${l}`))})]}),t.explanation&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),e.jsx("p",{className:"text-gray-300 text-sm",children:t.explanation})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",typeof t.difficulty_level=="string"?X(t.difficulty_level):X(oe(t.difficulty_level))]}),e.jsxs("span",{children:["Attempted: ",t.times_attempted||0," times"]}),e.jsxs("span",{children:["Correct: ",t.times_correct||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>B(t),className:"text-gray-400 hover:text-primary-400 p-1",title:"Edit question",children:"✏️"}),e.jsx("button",{onClick:()=>G(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"🗑️"})]})]})},t.id))})]})]})},Ie=({enabled:r,onChange:h,disabled:i=!1,className:x="",label:o="Shuffle Cards",description:u="Randomize the order of flashcards during study sessions"})=>{const n=()=>{i||h(!r)},C=v=>{(v.key===" "||v.key==="Enter")&&(v.preventDefault(),n())};return e.jsxs("div",{className:`flex items-center justify-between ${x}`,children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:o}),u&&e.jsx("span",{className:"text-xs text-gray-400",children:u})]})}),e.jsx("button",{type:"button",role:"switch","aria-checked":r,"aria-label":`${r?"Disable":"Enable"} ${o.toLowerCase()}`,onClick:n,onKeyDown:C,disabled:i,className:`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800
          ${i?"opacity-50 cursor-not-allowed bg-gray-600":r?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 hover:bg-gray-500"}
        `,children:e.jsx("span",{className:`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
            ${r?"translate-x-6":"translate-x-1"}
          `})})]})},Pe=()=>{const{id:r}=je(),h=we(),{studySetContent:i,isLoading:x,error:o,fetchStudySetContent:u}=ae(),{alert:n,confirm:C,prompt:v}=ee(),{settings:A,updateSettings:F}=ne(),[c,w]=g.useState(null),[b,O]=g.useState("study"),[L,q]=g.useState("flashcards"),[D,M]=g.useState([]),[N,T]=g.useState([]),[p,E]=g.useState("");g.useEffect(()=>{r&&u(r).catch(console.error)},[r,u]),g.useEffect(()=>{i!=null&&i.studySet&&(E(i.studySet.name),M(i.flashcards||[]),T(i.questions||[]))},[i]);const U=async()=>{if(!(!r||!c))try{const t=(A==null?void 0:A.shuffle_flashcards)||!1;await ae.getState().startStudySession(r,c,t),h(`/study/${r}/${c}`)}catch(t){await n({title:"Error",message:t.message||"Failed to start study session",variant:"error"})}},R=async()=>{if(!r||!(i!=null&&i.studySet))return;const t=await v({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:i.studySet.name});if(!(t===null||t.trim()===i.studySet.name)){if(!t.trim()){await n({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"});return}try{if(!(await fetch(`/api/study-sets/${r}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({name:t.trim()})})).ok)throw new Error("Failed to rename study set");E(t.trim()),await n({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await u(r)}catch(s){await n({title:"Error",message:s.message||"Failed to rename study set",variant:"error"})}}},m=async()=>{if(!(!r||!(i!=null&&i.studySet)||!await C({title:"Delete Study Set",message:`Are you sure you want to delete "${i.studySet.name}"?

This action cannot be undone and will delete all flashcards and quiz questions in this set.`,variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"})))try{if(!(await fetch(`/api/study-sets/${r}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete study set");await n({title:"Success",message:"Study set deleted successfully!",variant:"success"}),h("/dashboard")}catch(s){await n({title:"Error",message:s.message||"Failed to delete study set",variant:"error"})}},k=t=>{M(s=>[...s,t])},H=t=>{M(s=>s.map(l=>l.id===t.id?t:l))},G=t=>{M(s=>s.filter(l=>l.id!==t))},B=t=>{M(s=>[...s,...t])},V=t=>{T(s=>[...s,t])},J=t=>{T(s=>s.map(l=>l.id===t.id?t:l))},$=t=>{T(s=>s.filter(l=>l.id!==t))},P=t=>{T(s=>[...s,...t])};if(x)return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(o||!(i!=null&&i.studySet))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-red-400 mb-4",children:o||"Study set not found"}),e.jsx(S,{onClick:()=>h("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const{studySet:j}=i,Q=D&&D.length>0,Y=N&&N.length>0;return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("button",{onClick:()=>h("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Study Sets"}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:p}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(S,{onClick:R,variant:"secondary",size:"sm",children:"✏️ Rename"}),e.jsx(S,{onClick:m,variant:"danger",size:"sm",children:"🗑️ Delete"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[e.jsx("span",{className:"capitalize",children:j.type}),j.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.jsxs("span",{children:["Created ",new Date(j.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-600",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>O("study"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${b==="study"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"📚 Study Mode"}),e.jsx("button",{onClick:()=>O("manage"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${b==="manage"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"⚙️ Manage Content"})]})})}),b==="study"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[Q&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${c==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>w("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"🃏"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[D==null?void 0:D.length," flashcards • Interactive review"]})]})]})}),Y&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${c==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>w("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"📝"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[N==null?void 0:N.length," questions • Test your knowledge"]})]})]})})]}),c&&A&&e.jsx("div",{className:"mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600",children:e.jsx(Ie,{enabled:A.shuffle_flashcards,onChange:async t=>{try{await F({shuffle_flashcards:t})}catch(s){console.error("Failed to update shuffle setting:",s)}},label:"Shuffle Cards",description:"Randomize the order of flashcards during study sessions"})}),e.jsx(S,{onClick:U,disabled:!c,className:"w-full",size:"lg",children:c?`Start ${c==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]})}),b==="manage"&&r&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex space-x-1 mb-6",children:[e.jsxs("button",{onClick:()=>q("flashcards"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${L==="flashcards"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["📚 Flashcards (",D.length,")"]}),e.jsxs("button",{onClick:()=>q("quiz"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${L==="quiz"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["❓ Quiz Questions (",N.length,")"]})]}),L==="flashcards"&&e.jsx(Te,{studySetId:r,flashcards:D,onFlashcardAdded:k,onFlashcardUpdated:H,onFlashcardDeleted:G,onFlashcardsGenerated:B}),L==="quiz"&&e.jsx(Ge,{studySetId:r,questions:N,onQuestionAdded:V,onQuestionUpdated:J,onQuestionDeleted:$,onQuestionsGenerated:P})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[Q&&e.jsxs("div",{children:[D.length," flashcards"]}),Y&&e.jsxs("div",{children:[N==null?void 0:N.length," quiz questions"]}),!Q&&!Y&&e.jsx("div",{className:"text-gray-500",children:"No content yet"})]})]}),j.source_documents&&j.source_documents.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),e.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:j.source_documents.map((t,s)=>e.jsx("div",{children:t.filename},s))})]}),j.custom_prompt&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),e.jsx("p",{className:"text-sm text-gray-400",children:j.custom_prompt})]})]})]})]})};export{Pe as StudySetPage};
