import{r}from"./index-204383b4.js";const w=()=>{const[u,i]=r.useState(null),[l,c]=r.useState(!0),[g,s]=r.useState(null),a=r.useCallback(async()=>{try{c(!0),s(null);const e=localStorage.getItem("token");if(!e)throw new Error("No authentication token found");const t=await fetch("/api/user/settings",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok){const n=await t.json();throw new Error(n.error||"Failed to fetch user settings")}const o=await t.json();i(o.data)}catch(e){s(e.message),console.error("Error fetching user settings:",e)}finally{c(!1)}},[]),h=r.useCallback(async e=>{try{s(null);const t=localStorage.getItem("token");if(!t)throw new Error("No authentication token found");const o=await fetch("/api/user/settings",{method:"PATCH",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify(e)});if(!o.ok){const d=await o.json();throw new Error(d.error||"Failed to update user settings")}const n=await o.json();i(n.data)}catch(t){throw s(t.message),console.error("Error updating user settings:",t),t}},[]),f=r.useCallback(async()=>{await a()},[a]);return r.useEffect(()=>{a()},[a]),{settings:u,loading:l,error:g,updateSettings:h,refetch:f}};export{w as u};
