import{c as m}from"./index-14943559.js";const S=s=>{const o=[...s];for(let t=o.length-1;t>0;t--){const e=Math.floor(Math.random()*(t+1));[o[t],o[e]]=[o[e],o[t]]}return o},w=m((s,o)=>({currentSession:null,studySetContent:null,studySets:[],sessions:[],isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async(t,e=!1)=>{var r;const{studySetContent:n}=o();if(!(e&&((r=n==null?void 0:n.studySet)==null?void 0:r.id)===t)){s({isLoading:!0,error:null});try{const a=localStorage.getItem("auth_token"),i=await fetch(`/api/study-sets/${t}/content`,{headers:{Authorization:`Bearer ${a}`}});if(!i.ok){const u=await i.json();throw new Error(u.error||"Failed to fetch study set content")}const c=await i.json();if(c.success)s({studySetContent:{studySet:c.data.studySet,flashcards:c.data.studySet.type==="flashcards"?c.data.content:void 0,questions:c.data.studySet.type==="quiz"?c.data.content:void 0},isLoading:!1});else throw new Error(c.error)}catch(a){throw s({error:a.message||"Failed to fetch study set content",isLoading:!1}),a}}},startStudySession:async(t,e,n=!1)=>{var f,h,y;const{studySetContent:r,fetchStudySetContent:a}=o();(!r||((f=r.studySet)==null?void 0:f.id)!==t)&&await a(t);const i=o().studySetContent;if(!i)throw new Error("Failed to load study set content");const c=e==="flashcards"?((h=i.flashcards)==null?void 0:h.length)||0:((y=i.questions)==null?void 0:y.length)||0;if(c===0)throw new Error("No study materials found in this set");let u;if(n){if(u=Array.from({length:c},(l,d)=>d),e==="flashcards"&&i.flashcards){const l=S(i.flashcards);s(d=>({studySetContent:{...d.studySetContent,flashcards:l}}))}else if(e==="quiz"&&i.questions){const l=S(i.questions);s(d=>({studySetContent:{...d.studySetContent,questions:l}}))}}s({currentSession:{studySetId:t,type:e,startTime:new Date,currentIndex:0,totalItems:c,reviewedItems:[],flaggedItems:[],correctAnswers:e==="quiz"?0:void 0,timeSpent:0,isShuffled:n,originalOrder:u}})},endStudySession:()=>{s({currentSession:null})},nextItem:()=>{const{currentSession:t,addToHistory:e}=o();if(!t)return;const n=t.currentIndex===t.totalItems-1?0:t.currentIndex+1;e({type:"NEXT_ITEM",payload:{fromIndex:t.currentIndex,toIndex:n},previousState:{currentIndex:t.currentIndex},timestamp:Date.now()}),s({currentSession:{...t,currentIndex:n}})},previousItem:()=>{const{currentSession:t,addToHistory:e}=o();if(!t)return;const n=t.currentIndex===0?t.totalItems-1:t.currentIndex-1;e({type:"PREVIOUS_ITEM",payload:{fromIndex:t.currentIndex,toIndex:n},previousState:{currentIndex:t.currentIndex},timestamp:Date.now()}),s({currentSession:{...t,currentIndex:n}})},goToItem:t=>{const{currentSession:e}=o();if(!e)return;const n=Math.max(0,Math.min(t,e.totalItems-1));s({currentSession:{...e,currentIndex:n}})},toggleFlag:t=>{const{currentSession:e,addToHistory:n}=o();if(!e)return;const r=e.flaggedItems.includes(t),a=r?e.flaggedItems.filter(i=>i!==t):[...e.flaggedItems,t];n({type:"TOGGLE_FLAG",payload:{itemId:t,wasFlagged:r},previousState:{flaggedItems:e.flaggedItems},timestamp:Date.now()}),s({currentSession:{...e,flaggedItems:a}})},markReviewed:t=>{const{currentSession:e}=o();e&&(e.reviewedItems.includes(e.currentIndex)||s({currentSession:{...e,reviewedItems:[...e.reviewedItems,e.currentIndex]}}))},submitQuizAnswer:(t,e,n)=>{const{currentSession:r,markReviewed:a}=o();!r||r.type!=="quiz"||(a(t),n&&s({currentSession:{...r,correctAnswers:(r.correctAnswers||0)+1}}))},updateTimeSpent:t=>{const{currentSession:e}=o();e&&s({currentSession:{...e,timeSpent:e.timeSpent+t}})},addToHistory:t=>{const{actionHistory:e,currentActionIndex:n}=o(),r=e.slice(0,n+1);r.push(t);const a=r.slice(-50);s({actionHistory:a,currentActionIndex:a.length-1,canUndo:a.length>0,canRedo:!1})},undo:()=>{const{actionHistory:t,currentActionIndex:e,currentSession:n}=o();if(e<0||!n)return;const r=t[e];s({currentSession:{...n,...r.previousState},currentActionIndex:e-1,canUndo:e>0,canRedo:!0})},redo:()=>{const{actionHistory:t,currentActionIndex:e,currentSession:n}=o();if(e>=t.length-1||!n)return;const r=e+1,a=t[r];switch(a.type){case"NEXT_ITEM":o().nextItem();break;case"PREVIOUS_ITEM":o().previousItem();break;case"TOGGLE_FLAG":o().toggleFlag(a.payload.itemId);break;case"MARK_REVIEWED":o().markReviewed(a.payload.itemId);break}s({currentActionIndex:r,canUndo:!0,canRedo:r<t.length-1})},clearHistory:()=>{s({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})},fetchStudySets:async()=>{s({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),e=await fetch("/api/study-sets",{headers:{Authorization:`Bearer ${t}`}});if(!e.ok){const r=await e.json();throw new Error(r.error||"Failed to fetch study sets")}const n=await e.json();if(n.success)s({studySets:n.data,isLoading:!1});else throw new Error(n.error)}catch(t){throw s({error:t.message||"Failed to fetch study sets",isLoading:!1}),t}},fetchStudySessions:async(t="30d")=>{s({isLoading:!0,error:null});try{const e=localStorage.getItem("auth_token"),n=await fetch(`/api/study-sessions?timeRange=${t}`,{headers:{Authorization:`Bearer ${e}`}});if(!n.ok){const a=await n.json();throw new Error(a.error||"Failed to fetch study sessions")}const r=await n.json();if(r.success){const a=r.data.map(i=>({...i,startTime:new Date(i.startTime),endTime:i.endTime?new Date(i.endTime):void 0}));s({sessions:a,isLoading:!1})}else throw new Error(r.error)}catch(e){throw s({error:e.message||"Failed to fetch study sessions",isLoading:!1}),e}}}));export{w as u};
