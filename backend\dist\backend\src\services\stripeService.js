"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const supabaseService_1 = require("./supabaseService");
const creditService_1 = require("./creditService");
class StripeService {
    constructor() {
        const secretKey = process.env.STRIPE_SECRET_KEY;
        if (!secretKey) {
            throw new Error('STRIPE_SECRET_KEY environment variable is required');
        }
        this.stripe = new stripe_1.default(secretKey, {
            apiVersion: '2022-11-15',
        });
    }
    // Create or retrieve Stripe customer
    async createOrGetCustomer(userId, email, name) {
        try {
            // Check if user already has a Stripe customer ID
            const { data: profile } = await supabaseService_1.supabase
                .from('users')
                .select('stripe_customer_id')
                .eq('id', userId)
                .single();
            if (profile?.stripe_customer_id) {
                return profile.stripe_customer_id;
            }
            // Create new Stripe customer
            const customer = await this.stripe.customers.create({
                email,
                name,
                metadata: {
                    user_id: userId,
                },
            });
            // Update user profile with Stripe customer ID
            await supabaseService_1.supabase
                .from('users')
                .update({ stripe_customer_id: customer.id })
                .eq('id', userId);
            return customer.id;
        }
        catch (error) {
            console.error('Error creating/getting Stripe customer:', error);
            throw new Error('Failed to create or retrieve customer');
        }
    }
    // Create subscription
    async createSubscription(customerId, priceId) {
        try {
            const subscription = await this.stripe.subscriptions.create({
                customer: customerId,
                items: [{ price: priceId }],
                payment_behavior: 'default_incomplete',
                payment_settings: { save_default_payment_method: 'on_subscription' },
                expand: ['latest_invoice.payment_intent'],
            });
            return subscription;
        }
        catch (error) {
            console.error('Error creating subscription:', error);
            throw new Error('Failed to create subscription');
        }
    }
    // Get customer subscriptions
    async getCustomerSubscriptions(customerId) {
        try {
            const subscriptions = await this.stripe.subscriptions.list({
                customer: customerId,
                status: 'all',
                expand: ['data.default_payment_method'],
            });
            return subscriptions.data;
        }
        catch (error) {
            console.error('Error getting customer subscriptions:', error);
            throw new Error('Failed to get subscriptions');
        }
    }
    // Cancel subscription
    async cancelSubscription(subscriptionId) {
        try {
            const subscription = await this.stripe.subscriptions.update(subscriptionId, {
                cancel_at_period_end: true,
            });
            return subscription;
        }
        catch (error) {
            console.error('Error canceling subscription:', error);
            throw new Error('Failed to cancel subscription');
        }
    }
    // Create payment intent for credit purchase
    async createCreditPurchaseIntent(customerId, amount, credits) {
        try {
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount: amount * 100, // Convert to cents
                currency: 'usd',
                customer: customerId,
                metadata: {
                    type: 'credit_purchase',
                    credits: credits.toString(),
                },
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            return paymentIntent;
        }
        catch (error) {
            console.error('Error creating payment intent:', error);
            throw new Error('Failed to create payment intent');
        }
    }
    // Get customer invoices
    async getCustomerInvoices(customerId, limit = 10) {
        try {
            const invoices = await this.stripe.invoices.list({
                customer: customerId,
                limit,
                expand: ['data.payment_intent'],
            });
            return invoices.data;
        }
        catch (error) {
            console.error('Error getting customer invoices:', error);
            throw new Error('Failed to get invoices');
        }
    }
    // Get customer payment methods
    async getCustomerPaymentMethods(customerId) {
        try {
            const paymentMethods = await this.stripe.paymentMethods.list({
                customer: customerId,
                type: 'card',
            });
            return paymentMethods.data;
        }
        catch (error) {
            console.error('Error getting payment methods:', error);
            throw new Error('Failed to get payment methods');
        }
    }
    // Handle webhook events
    async handleWebhook(body, signature) {
        const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
        if (!webhookSecret) {
            throw new Error('STRIPE_WEBHOOK_SECRET environment variable is required');
        }
        try {
            const event = this.stripe.webhooks.constructEvent(body, signature, webhookSecret);
            switch (event.type) {
                case 'payment_intent.succeeded':
                    await this.handlePaymentSuccess(event.data.object);
                    break;
                case 'invoice.payment_succeeded':
                    await this.handleInvoicePaymentSuccess(event.data.object);
                    break;
                case 'customer.subscription.updated':
                    await this.handleSubscriptionUpdate(event.data.object);
                    break;
                case 'customer.subscription.deleted':
                    await this.handleSubscriptionCancellation(event.data.object);
                    break;
                default:
                    console.log(`Unhandled event type: ${event.type}`);
            }
            return { received: true };
        }
        catch (error) {
            console.error('Webhook error:', error);
            throw error;
        }
    }
    // Handle successful payment
    async handlePaymentSuccess(paymentIntent) {
        if (paymentIntent.metadata.type === 'credit_purchase') {
            const credits = parseInt(paymentIntent.metadata.credits);
            const customerId = paymentIntent.customer;
            // Get user ID from customer
            const { data: user } = await supabaseService_1.supabase
                .from('users')
                .select('id')
                .eq('stripe_customer_id', customerId)
                .single();
            if (user) {
                await creditService_1.creditService.addCredits(user.id, credits, 'stripe_purchase', paymentIntent.id);
            }
        }
    }
    // Handle successful invoice payment (subscriptions)
    async handleInvoicePaymentSuccess(invoice) {
        const customerId = invoice.customer;
        // Get user ID from customer
        const { data: user } = await supabaseService_1.supabase
            .from('users')
            .select('id')
            .eq('stripe_customer_id', customerId)
            .single();
        if (user) {
            // Add subscription credits based on plan
            const subscriptionCredits = this.getSubscriptionCredits(invoice.amount_paid);
            if (subscriptionCredits > 0) {
                await creditService_1.creditService.addCredits(user.id, subscriptionCredits, 'subscription_payment', invoice.id);
            }
        }
    }
    // Handle subscription updates
    async handleSubscriptionUpdate(subscription) {
        const customerId = subscription.customer;
        // Update user subscription status in database
        await supabaseService_1.supabase
            .from('users')
            .update({
            subscription_status: subscription.status,
            subscription_id: subscription.id,
        })
            .eq('stripe_customer_id', customerId);
    }
    // Handle subscription cancellation
    async handleSubscriptionCancellation(subscription) {
        const customerId = subscription.customer;
        // Update user subscription status in database
        await supabaseService_1.supabase
            .from('users')
            .update({
            subscription_status: 'canceled',
            subscription_id: null,
        })
            .eq('stripe_customer_id', customerId);
    }
    // Get subscription credits based on amount paid
    getSubscriptionCredits(amountPaid) {
        // Convert cents to dollars and determine credits
        const dollars = amountPaid / 100;
        if (dollars >= 99.99)
            return 1000; // Pro Annual
        if (dollars >= 9.99)
            return 100; // Pro Monthly
        return 0; // Free plan
    }
    // Get Stripe prices for products
    async getPrices() {
        try {
            const prices = await this.stripe.prices.list({
                active: true,
                expand: ['data.product'],
            });
            return prices.data;
        }
        catch (error) {
            console.error('Error getting prices:', error);
            throw new Error('Failed to get prices');
        }
    }
}
exports.stripeService = new StripeService();
//# sourceMappingURL=stripeService.js.map