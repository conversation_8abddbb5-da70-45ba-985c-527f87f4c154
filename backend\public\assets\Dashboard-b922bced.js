import{r as n,u as b,j as e,B as j}from"./index-204383b4.js";import{u as N}from"./index-a4742d38.js";import{u as w}from"./documentStore-9e7af976.js";const k=()=>{const[t,d]=n.useState(!1),[u,h]=n.useState([]),[g,i]=n.useState([]),{uploadDocument:f,setUploadProgress:o}=w(),m=b(),s=n.useCallback(async r=>{d(!0),h([]),i([]);const l=[],y=[];for(const a of r)try{if(a.size>10*1024*1024){l.push(`${a.name}: File size exceeds 10MB limit`);continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(a.type)){l.push(`${a.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);continue}o(a.name,0),await f(a),o(a.name,100),y.push(a.name)}catch(v){l.push(`${a.name}: ${v instanceof Error?v.message:"Unknown error"}`)}h(l),i(y),d(!1),y.length>0&&setTimeout(()=>i([]),3e3)},[f,o]),{getRootProps:c,getInputProps:x,isDragActive:p}=N({onDrop:s,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,disabled:t});return e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{...c(),className:`
          border-2 border-dashed rounded-xl p-12 text-center cursor-pointer transition-all duration-200
          ${p?"border-primary-500 bg-primary-500/10 scale-[1.02]":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5"}
          ${t?"opacity-50 cursor-not-allowed":""}
          bg-background-secondary/50
        `,children:[e.jsx("input",{...x()}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:`
              p-4 rounded-full transition-colors
              ${p?"bg-primary-500/20":"bg-gray-800/50"}
            `,children:e.jsx("svg",{className:`h-16 w-16 transition-colors ${p?"text-primary-400":"text-gray-400"}`,stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:e.jsx("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})})}),p?e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-primary-400 mb-2",children:"Drop your documents here"}),e.jsx("p",{className:"text-gray-300",children:"Release to upload your files"})]}):e.jsxs("div",{children:[e.jsx("h3",{className:"text-2xl font-bold text-white mb-2",children:"Upload Your Documents"}),e.jsxs("p",{className:"text-lg text-gray-300 mb-2",children:["Drag & drop files here, or"," ",e.jsx("span",{className:"text-primary-500 font-semibold hover:text-primary-400 transition-colors",children:"browse to select"})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Supports PDF, DOCX, TXT, PPTX files (max 10MB each)"})]}),!p&&!t&&e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 pt-4",children:[e.jsx("button",{onClick:r=>{r.stopPropagation(),m("/documents")},className:"text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2",children:"📄 Manage existing documents"}),e.jsx("span",{className:"hidden sm:block text-gray-600",children:"•"}),e.jsx("button",{onClick:r=>{r.stopPropagation(),m("/create-study-set")},className:"text-sm text-gray-400 hover:text-primary-400 transition-colors flex items-center gap-2",children:"📚 Create study set"})]})]})]}),t&&e.jsxs("div",{className:"mt-4 bg-background-secondary rounded-lg p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"}),e.jsx("p",{className:"text-sm font-medium text-gray-300",children:"Uploading files..."})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-primary-500 h-2 rounded-full animate-pulse",style:{width:"60%"}})})]}),g.length>0&&e.jsxs("div",{className:"mt-4 bg-green-900/20 border border-green-700 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("svg",{className:"h-5 w-5 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("h4",{className:"text-green-400 font-medium",children:"Successfully uploaded:"})]}),e.jsx("ul",{className:"text-sm text-green-300 space-y-1",children:g.map((r,l)=>e.jsxs("li",{children:["• ",r]},l))})]}),u.length>0&&e.jsxs("div",{className:"mt-4 bg-red-900/20 border border-red-700 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("svg",{className:"h-5 w-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),e.jsx("h4",{className:"text-red-400 font-medium",children:"Upload errors:"})]}),e.jsx("ul",{className:"text-sm text-red-300 space-y-1",children:u.map((r,l)=>e.jsxs("li",{children:["• ",r]},l))})]})]})},L=n.memo(()=>{const t=b(),[d,u]=n.useState([]),[h,g]=n.useState(!0),[i,f]=n.useState(null);n.useEffect(()=>{o()},[]);const o=async()=>{try{const s=localStorage.getItem("auth_token"),c=await fetch("/api/study-sets",{headers:{Authorization:`Bearer ${s}`}});if(!c.ok)throw new Error("Failed to fetch study sets");const x=await c.json();if(x.success)u(x.data);else throw new Error(x.error||"Failed to fetch study sets")}catch(s){f(s.message)}finally{g(!1)}},m=s=>new Date(s).toLocaleDateString();return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:"Your Study Sets"}),e.jsx("p",{className:"text-gray-400 mt-2",children:"Create, manage, and study your flashcard sets and quizzes"})]}),e.jsx("div",{className:"flex space-x-4",children:e.jsx(j,{onClick:()=>t("/create-study-set"),variant:"primary",children:"Create Study Set"})})]}),e.jsx(k,{}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Study Sets"}),h?e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study sets..."})]}):i?e.jsxs("div",{className:"text-center py-12",children:[e.jsxs("div",{className:"text-red-400 mb-4",children:["Error: ",i]}),e.jsx(j,{onClick:o,variant:"secondary",children:"Try Again"})]}):d.length===0?e.jsxs("div",{className:"text-center py-12 bg-gray-800/50 rounded-lg",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No study sets found"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Create your first study set to get started with studying"}),e.jsx("div",{className:"flex justify-center",children:e.jsx(j,{onClick:()=>t("/create-study-set"),variant:"primary",children:"Create Study Set"})})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(s=>e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer",onClick:()=>t(`/study-sets/${s.id}`),children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white truncate",children:s.name}),e.jsx("span",{className:"text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:s.type})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Items:"}),e.jsx("span",{children:s.type==="flashcards"?s.flashcard_count||0:s.quiz_question_count||0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Created:"}),e.jsx("span",{children:m(s.created_at)})]}),s.last_studied_at&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Last studied:"}),e.jsx("span",{children:m(s.last_studied_at)})]}),s.is_ai_generated&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{className:"text-xs",children:"🤖"}),e.jsx("span",{children:"AI Generated"})]})]}),e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:e.jsx("div",{onClick:c=>{c.stopPropagation(),t(`/study-sets/${s.id}`)},children:e.jsx(j,{variant:"secondary",size:"sm",className:"w-full",children:"Start Studying"})})})]},s.id))})]})]})})});export{L as Dashboard};
