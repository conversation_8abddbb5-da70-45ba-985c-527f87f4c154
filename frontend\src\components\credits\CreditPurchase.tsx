import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  HiCreditCard, 
  HiSparkles, 
  HiCheck,
  HiStar,
  HiLightningBolt
} from 'react-icons/hi';
import { Button } from '../common/Button';
import { useCreditStore } from '../../stores/creditStore';

interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  bonus?: number;
  popular?: boolean;
  description: string;
  features: string[];
}

interface CreditPurchaseProps {
  currentBalance: number;
  userTier: string;
  onPurchaseComplete: () => void;
}

const creditPackages: CreditPackage[] = [
  {
    id: 'starter',
    name: 'Starter Pack',
    credits: 100,
    price: 9.99,
    description: 'Perfect for trying out AI features',
    features: [
      '100 AI generation credits (5,000 generations)',
      'Valid for 6 months',
      'All AI features included'
    ]
  },
  {
    id: 'popular',
    name: 'Popular Pack',
    credits: 500,
    price: 39.99,
    bonus: 50,
    popular: true,
    description: 'Most popular choice for regular users',
    features: [
      '500 AI generation credits (25,000 generations)',
      '+50 bonus credits (2,500 generations)',
      'Valid for 12 months',
      'Priority support',
      'All AI features included'
    ]
  },
  {
    id: 'power',
    name: 'Power User',
    credits: 1000,
    price: 69.99,
    bonus: 200,
    description: 'For heavy AI users and professionals',
    features: [
      '1,000 AI generation credits (50,000 generations)',
      '+200 bonus credits (10,000 generations)',
      'Valid for 12 months',
      'Priority support',
      'Early access to new features',
      'All AI features included'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    credits: 2500,
    price: 149.99,
    bonus: 750,
    description: 'Maximum value for teams and businesses',
    features: [
      '2,500 AI generation credits (125,000 generations)',
      '+750 bonus credits (37,500 generations)',
      'Valid for 18 months',
      'Dedicated support',
      'Early access to new features',
      'Custom integrations available',
      'All AI features included'
    ]
  }
];

export const CreditPurchase: React.FC<CreditPurchaseProps> = ({
  currentBalance,
  onPurchaseComplete
}) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const { purchaseCredits } = useCreditStore();

  const handlePurchase = async (packageId: string) => {
    const selectedPkg = creditPackages.find(pkg => pkg.id === packageId);
    if (!selectedPkg) return;

    setIsProcessing(true);
    setSelectedPackage(packageId);

    try {
      // In a real implementation, this would integrate with Stripe
      // For now, we'll simulate the purchase process
      const result = await purchaseCredits(selectedPkg.credits + (selectedPkg.bonus || 0));
      
      if (result.success) {
        onPurchaseComplete();
        // Show success message or redirect
      } else {
        console.error('Purchase failed:', result.error);
      }
    } catch (error) {
      console.error('Purchase error:', error);
    } finally {
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  const getPackageIcon = (packageId: string) => {
    switch (packageId) {
      case 'starter':
        return <HiCreditCard className="w-6 h-6" />;
      case 'popular':
        return <HiStar className="w-6 h-6" />;
      case 'power':
        return <HiLightningBolt className="w-6 h-6" />;
      case 'enterprise':
        return <HiSparkles className="w-6 h-6" />;
      default:
        return <HiCreditCard className="w-6 h-6" />;
    }
  };

  const getDiscountPercentage = (credits: number, bonus: number, price: number) => {
    if (!bonus) return null;
    const totalCredits = credits + bonus;
    const regularPrice = (totalCredits / credits) * price;
    const discount = ((regularPrice - price) / regularPrice) * 100;
    return Math.round(discount);
  };

  return (
    <div className="space-y-6">
      {/* Current Balance Display */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">Current Balance</h3>
            <p className="text-gray-400">Your available credits</p>
          </div>
          <div className="text-right">
            <span className="text-2xl font-bold text-primary-400">{currentBalance}</span>
            <span className="text-gray-400 ml-2">credits</span>
          </div>
        </div>
      </div>

      {/* Credit Packages */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-6">Choose a Credit Package</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {creditPackages.map((pkg, index) => {
            const totalCredits = pkg.credits + (pkg.bonus || 0);
            const discount = pkg.bonus ? getDiscountPercentage(pkg.credits, pkg.bonus, pkg.price) : null;
            const isSelected = selectedPackage === pkg.id;
            const isProcessingThis = isProcessing && isSelected;

            return (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`
                  relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                  ${pkg.popular 
                    ? 'border-primary-500 ring-2 ring-primary-500/20' 
                    : 'border-border-primary hover:border-border-secondary'
                  }
                  ${isSelected ? 'ring-2 ring-primary-500/50' : ''}
                `}
              >
                {/* Popular Badge */}
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Most Popular
                    </div>
                  </div>
                )}

                {/* Discount Badge */}
                {discount && (
                  <div className="absolute -top-2 -right-2">
                    <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {discount}% OFF
                    </div>
                  </div>
                )}

                <div className="text-center">
                  {/* Icon */}
                  <div className={`inline-flex p-3 rounded-lg mb-4 ${
                    pkg.popular ? 'bg-primary-500/20 text-primary-400' : 'bg-background-tertiary text-gray-400'
                  }`}>
                    {getPackageIcon(pkg.id)}
                  </div>

                  {/* Package Name */}
                  <h4 className="text-lg font-semibold text-white mb-2">{pkg.name}</h4>
                  
                  {/* Credits */}
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-white">{pkg.credits}</span>
                    {pkg.bonus && (
                      <span className="text-green-400 text-sm ml-1">+{pkg.bonus}</span>
                    )}
                    <div className="text-gray-400 text-sm">credits</div>
                    {pkg.bonus && (
                      <div className="text-green-400 text-xs">
                        Total: {totalCredits} credits
                      </div>
                    )}
                  </div>

                  {/* Price */}
                  <div className="mb-4">
                    <span className="text-2xl font-bold text-white">${pkg.price}</span>
                    <div className="text-gray-400 text-sm">
                      ${(pkg.price / totalCredits).toFixed(3)} per credit
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-400 text-sm mb-4">{pkg.description}</p>

                  {/* Features */}
                  <div className="space-y-2 mb-6">
                    {pkg.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-gray-300">
                        <HiCheck className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Purchase Button */}
                  <Button
                    onClick={() => handlePurchase(pkg.id)}
                    variant={pkg.popular ? "primary" : "secondary"}
                    className="w-full"
                    isLoading={isProcessingThis}
                    disabled={isProcessing}
                  >
                    {isProcessingThis ? 'Processing...' : `Purchase ${pkg.name}`}
                  </Button>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Additional Information */}
      <div className="bg-background-secondary rounded-lg p-6 border border-border-primary">
        <h4 className="text-lg font-semibold text-white mb-4">Payment Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-white mb-2">Secure Payment</h5>
            <p className="text-gray-400 text-sm">
              All payments are processed securely through Stripe. We never store your payment information.
            </p>
          </div>
          <div>
            <h5 className="font-medium text-white mb-2">Credit Expiration</h5>
            <p className="text-gray-400 text-sm">
              Credits are valid for the duration specified in each package. Unused credits will expire after the validity period.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
