import Stripe from 'stripe';
declare class StripeService {
    private stripe;
    constructor();
    createOrGetCustomer(userId: string, email: string, name?: string): Promise<string>;
    createSubscription(customerId: string, priceId: string): Promise<Stripe.Response<Stripe.Subscription>>;
    getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]>;
    cancelSubscription(subscriptionId: string): Promise<Stripe.Response<Stripe.Subscription>>;
    createCreditPurchaseIntent(customerId: string, amount: number, credits: number): Promise<Stripe.Response<Stripe.PaymentIntent>>;
    getCustomerInvoices(customerId: string, limit?: number): Promise<Stripe.Invoice[]>;
    getCustomerPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]>;
    handleWebhook(body: string, signature: string): Promise<{
        received: boolean;
    }>;
    private handlePaymentSuccess;
    private handleInvoicePaymentSuccess;
    private handleSubscriptionUpdate;
    private handleSubscriptionCancellation;
    private getSubscriptionCredits;
    getPrices(): Promise<Stripe.Price[]>;
}
export declare const stripeService: StripeService;
export {};
//# sourceMappingURL=stripeService.d.ts.map