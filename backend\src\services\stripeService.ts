import Stripe from 'stripe';
import { supabase } from './supabaseService';
import { creditService } from './creditService';

class StripeService {
  private stripe: Stripe;

  constructor() {
    const secretKey = process.env.STRIPE_SECRET_KEY;
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    
    this.stripe = new Stripe(secretKey, {
      apiVersion: '2023-10-16',
    });
  }

  // Create or retrieve Stripe customer
  async createOrGetCustomer(userId: string, email: string, name?: string): Promise<string> {
    try {
      // Check if user already has a Stripe customer ID
      const { data: profile } = await supabase
        .from('users')
        .select('stripe_customer_id')
        .eq('id', userId)
        .single();

      if (profile?.stripe_customer_id) {
        return profile.stripe_customer_id;
      }

      // Create new Stripe customer
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          user_id: userId,
        },
      });

      // Update user profile with Stripe customer ID
      await supabase
        .from('users')
        .update({ stripe_customer_id: customer.id })
        .eq('id', userId);

      return customer.id;
    } catch (error) {
      console.error('Error creating/getting Stripe customer:', error);
      throw new Error('Failed to create or retrieve customer');
    }
  }

  // Create subscription
  async createSubscription(customerId: string, priceId: string) {
    try {
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
      });

      return subscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }

  // Get customer subscriptions
  async getCustomerSubscriptions(customerId: string) {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: 'all',
        expand: ['data.default_payment_method'],
      });

      return subscriptions.data;
    } catch (error) {
      console.error('Error getting customer subscriptions:', error);
      throw new Error('Failed to get subscriptions');
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string) {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });

      return subscription;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  // Create payment intent for credit purchase
  async createCreditPurchaseIntent(customerId: string, amount: number, credits: number) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: amount * 100, // Convert to cents
        currency: 'usd',
        customer: customerId,
        metadata: {
          type: 'credit_purchase',
          credits: credits.toString(),
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return paymentIntent;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw new Error('Failed to create payment intent');
    }
  }

  // Get customer invoices
  async getCustomerInvoices(customerId: string, limit: number = 10) {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit,
        expand: ['data.payment_intent'],
      });

      return invoices.data;
    } catch (error) {
      console.error('Error getting customer invoices:', error);
      throw new Error('Failed to get invoices');
    }
  }

  // Get customer payment methods
  async getCustomerPaymentMethods(customerId: string) {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return paymentMethods.data;
    } catch (error) {
      console.error('Error getting payment methods:', error);
      throw new Error('Failed to get payment methods');
    }
  }

  // Handle webhook events
  async handleWebhook(body: string, signature: string) {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET environment variable is required');
    }

    try {
      const event = this.stripe.webhooks.constructEvent(body, signature, webhookSecret);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSuccess(event.data.object as Stripe.PaymentIntent);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSuccess(event.data.object as Stripe.Invoice);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdate(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionCancellation(event.data.object as Stripe.Subscription);
          break;
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      console.error('Webhook error:', error);
      throw error;
    }
  }

  // Handle successful payment
  private async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    if (paymentIntent.metadata.type === 'credit_purchase') {
      const credits = parseInt(paymentIntent.metadata.credits);
      const customerId = paymentIntent.customer as string;

      // Get user ID from customer
      const { data: user } = await supabase
        .from('users')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .single();

      if (user) {
        await creditService.addCredits(
          user.id,
          credits,
          'stripe_purchase',
          paymentIntent.id
        );
      }
    }
  }

  // Handle successful invoice payment (subscriptions)
  private async handleInvoicePaymentSuccess(invoice: Stripe.Invoice) {
    const customerId = invoice.customer as string;
    
    // Get user ID from customer
    const { data: user } = await supabase
      .from('users')
      .select('id')
      .eq('stripe_customer_id', customerId)
      .single();

    if (user) {
      // Add subscription credits based on plan
      const subscriptionCredits = this.getSubscriptionCredits(invoice.amount_paid);
      if (subscriptionCredits > 0) {
        await creditService.addCredits(
          user.id,
          subscriptionCredits,
          'subscription_payment',
          invoice.id
        );
      }
    }
  }

  // Handle subscription updates
  private async handleSubscriptionUpdate(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    
    // Update user subscription status in database
    await supabase
      .from('users')
      .update({
        subscription_status: subscription.status,
        subscription_id: subscription.id,
      })
      .eq('stripe_customer_id', customerId);
  }

  // Handle subscription cancellation
  private async handleSubscriptionCancellation(subscription: Stripe.Subscription) {
    const customerId = subscription.customer as string;
    
    // Update user subscription status in database
    await supabase
      .from('users')
      .update({
        subscription_status: 'canceled',
        subscription_id: null,
      })
      .eq('stripe_customer_id', customerId);
  }

  // Get subscription credits based on amount paid
  private getSubscriptionCredits(amountPaid: number): number {
    // Convert cents to dollars and determine credits
    const dollars = amountPaid / 100;
    
    if (dollars >= 99.99) return 1000; // Pro Annual
    if (dollars >= 9.99) return 100;   // Pro Monthly
    
    return 0; // Free plan
  }

  // Get Stripe prices for products
  async getPrices() {
    try {
      const prices = await this.stripe.prices.list({
        active: true,
        expand: ['data.product'],
      });

      return prices.data;
    } catch (error) {
      console.error('Error getting prices:', error);
      throw new Error('Failed to get prices');
    }
  }
}

export const stripeService = new StripeService();
